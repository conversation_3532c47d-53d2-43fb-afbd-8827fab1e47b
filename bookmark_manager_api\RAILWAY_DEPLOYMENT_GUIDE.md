# Railway Deployment Guide for Bookmark Manager API

## 📋 Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **MongoDB Database**: Access to your Honor database

## 🚀 Step-by-Step Deployment

### Step 1: Create New Railway Project

1. Go to [railway.app](https://railway.app) and log in
2. Click **"New Project"**
3. Select **"Deploy from GitHub repo"**
4. Choose your repository containing the bookmark manager API
5. Select the `bookmark_manager_api` folder as the root directory

### Step 2: Configure Environment Variables

In Railway's project dashboard, go to **Variables** tab and add these environment variables:

#### Required Variables:

```bash
# MongoDB Configuration
MONGODB_URI=***********************************************************************
MONGODB_DATABASE=Honor

# Flask Configuration
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-here-change-this-in-production

# Railway will automatically set PORT variable
```

#### Variable Details:

- **MONGODB_URI**: Your MongoDB connection string
- **MONGODB_DATABASE**: Set to `Honor` (your database name)
- **FLASK_ENV**: Set to `production` for Railway deployment
- **SECRET_KEY**: Generate a secure secret key for Flask sessions

### Step 3: Verify Project Structure

Ensure your project has these files in the root directory:

```
bookmark_manager_api/
├── app.py              # Railway entry point
├── Procfile           # Process configuration
├── railway.json       # Railway configuration
├── requirements.txt   # Python dependencies
├── runtime.txt        # Python version (optional)
└── src/
    ├── main.py        # Flask application
    ├── database.py    # Database configuration
    ├── models/        # Data models
    └── routes/        # API routes
```

### Step 4: Deploy

1. Railway will automatically detect your project and start building
2. Monitor the build logs in Railway's dashboard
3. Once deployed, Railway will provide a public URL

### Step 5: Test Deployment

1. **Health Check**: Visit `https://your-app.railway.app/api/health`
2. **API Endpoints**: Test the bookmark endpoints
3. **Database Connection**: Verify data is loading from Honor database

## 🔧 Configuration Files

### railway.json
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "deploy": {
    "startCommand": "gunicorn --bind 0.0.0.0:$PORT app:app",
    "healthcheckPath": "/api/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### Procfile
```
web: gunicorn --bind 0.0.0.0:$PORT app:app
```

### requirements.txt
```
Flask>=3.0.0
flask-cors>=4.0.0
pymongo>=4.0.0
gunicorn>=20.0.0
```

## 🔍 Troubleshooting

### Common Issues:

1. **Database Connection Failed**
   - Verify MONGODB_URI is correct
   - Check MONGODB_DATABASE is set to "Honor"
   - Ensure MongoDB server is accessible from Railway

2. **Build Failures**
   - Check requirements.txt has all dependencies
   - Verify Python version compatibility
   - Review build logs in Railway dashboard

3. **App Won't Start**
   - Ensure app.py is in the root directory
   - Check Procfile configuration
   - Verify gunicorn is in requirements.txt

### Debug Commands:

```bash
# Check environment variables
echo $MONGODB_URI
echo $MONGODB_DATABASE
echo $PORT

# Test database connection
python -c "from src.database import init_database; init_database(None)"
```

## 📊 Monitoring

After deployment:

1. **Logs**: Monitor application logs in Railway dashboard
2. **Metrics**: Check CPU, memory, and network usage
3. **Health**: Regular health check at `/api/health`
4. **Database**: Monitor MongoDB connection status

## 🔐 Security Notes

1. **Environment Variables**: Never commit sensitive data to Git
2. **Secret Key**: Use a strong, unique secret key
3. **Database Access**: Ensure MongoDB credentials are secure
4. **CORS**: Configure CORS appropriately for production

## 📝 Next Steps

After successful deployment:

1. Update frontend API URL to point to Railway deployment
2. Set up custom domain (optional)
3. Configure monitoring and alerts
4. Set up automated backups for MongoDB data

## 🆘 Support

If you encounter issues:

1. Check Railway documentation: [docs.railway.app](https://docs.railway.app)
2. Review application logs in Railway dashboard
3. Test locally first to isolate deployment issues
4. Check MongoDB connectivity from Railway's network
