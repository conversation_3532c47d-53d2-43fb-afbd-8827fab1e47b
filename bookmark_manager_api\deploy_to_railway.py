#!/usr/bin/env python3
"""
Railway Deployment Helper Script
This script helps prepare and validate the project for Railway deployment
"""

import os
import sys
import json
from pathlib import Path

def check_file_exists(filepath, description):
    """Check if a required file exists"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def check_environment_variables():
    """Check if required environment variables are set"""
    required_vars = [
        ('MONGODB_URI', 'MongoDB connection string'),
        ('MONGODB_DATABASE', 'MongoDB database name (should be "Honor")'),
    ]
    
    optional_vars = [
        ('FLASK_ENV', 'Flask environment (should be "production" for Railway)'),
        ('SECRET_KEY', 'Flask secret key'),
        ('PORT', 'Application port (Railway sets this automatically)'),
    ]
    
    print("\n🔍 Checking Environment Variables:")
    print("=" * 50)
    
    all_good = True
    
    print("\nRequired Variables:")
    for var_name, description in required_vars:
        value = os.environ.get(var_name)
        if value:
            # Show partial value for security
            display_value = value[:20] + "..." if len(value) > 20 else value
            if var_name == 'MONGODB_DATABASE':
                display_value = value  # Show full database name
            print(f"✅ {var_name}: {display_value}")
        else:
            print(f"❌ {var_name}: NOT SET - {description}")
            all_good = False
    
    print("\nOptional Variables:")
    for var_name, description in optional_vars:
        value = os.environ.get(var_name)
        if value:
            display_value = value[:20] + "..." if len(value) > 20 else value
            print(f"✅ {var_name}: {display_value}")
        else:
            print(f"⚠️  {var_name}: NOT SET - {description}")
    
    return all_good

def validate_requirements():
    """Validate requirements.txt has necessary dependencies"""
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ {requirements_file} not found")
        return False
    
    with open(requirements_file, 'r') as f:
        content = f.read().lower()
    
    required_packages = ['flask', 'flask-cors', 'pymongo', 'gunicorn']
    missing_packages = []
    
    for package in required_packages:
        if package not in content:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages in requirements.txt: {', '.join(missing_packages)}")
        return False
    else:
        print("✅ All required packages found in requirements.txt")
        return True

def validate_railway_config():
    """Validate Railway configuration files"""
    print("\n🚂 Checking Railway Configuration:")
    print("=" * 50)
    
    all_good = True
    
    # Check railway.json
    if os.path.exists("railway.json"):
        try:
            with open("railway.json", 'r') as f:
                config = json.load(f)
            
            if 'deploy' in config and 'startCommand' in config['deploy']:
                print("✅ railway.json: Valid configuration")
            else:
                print("⚠️  railway.json: Missing deploy.startCommand")
        except json.JSONDecodeError:
            print("❌ railway.json: Invalid JSON format")
            all_good = False
    else:
        print("⚠️  railway.json: Not found (optional)")
    
    # Check Procfile
    if os.path.exists("Procfile"):
        with open("Procfile", 'r') as f:
            content = f.read().strip()
        
        if 'gunicorn' in content and 'app:app' in content:
            print("✅ Procfile: Valid configuration")
        else:
            print("⚠️  Procfile: May need adjustment")
    else:
        print("⚠️  Procfile: Not found (optional if railway.json exists)")
    
    return all_good

def main():
    """Main validation function"""
    print("🚀 Railway Deployment Validation")
    print("=" * 50)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Check required files
    print("\n📋 Checking Required Files:")
    print("=" * 50)
    
    files_ok = True
    required_files = [
        ("app.py", "Railway entry point"),
        ("requirements.txt", "Python dependencies"),
        ("src/main.py", "Flask application"),
        ("src/database.py", "Database configuration"),
    ]
    
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            files_ok = False
    
    # Check requirements
    print("\n📦 Checking Dependencies:")
    print("=" * 50)
    requirements_ok = validate_requirements()
    
    # Check Railway config
    railway_config_ok = validate_railway_config()
    
    # Check environment variables
    env_vars_ok = check_environment_variables()
    
    # Summary
    print("\n📊 Deployment Readiness Summary:")
    print("=" * 50)
    
    checks = [
        ("Required Files", files_ok),
        ("Dependencies", requirements_ok),
        ("Railway Config", railway_config_ok),
        ("Environment Variables", env_vars_ok),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 Project is ready for Railway deployment!")
        print("\nNext steps:")
        print("1. Push your code to GitHub")
        print("2. Create a new Railway project")
        print("3. Connect your GitHub repository")
        print("4. Set environment variables in Railway dashboard")
        print("5. Deploy!")
    else:
        print("⚠️  Please fix the issues above before deploying to Railway")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
