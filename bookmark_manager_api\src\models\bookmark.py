from bson import ObjectId
from datetime import datetime
from src.database import db_instance
from pymongo.errors import DuplicateKeyError
import re

class BookmarkModel:
    def __init__(self):
        self.collection = None
        self.tags_collection = None
    
    def _ensure_collections(self):
        """确保集合已初始化"""
        if self.collection is None:
            self.collection = db_instance.get_collection('CataPage')  # Using CataPage collection
        if self.tags_collection is None:
            self.tags_collection = db_instance.get_collection('tags')
    
    def create_bookmark(self, data):
        """创建新书签"""
        self._ensure_collections()
        
        bookmark = {
            'title': data.get('title', ''),
            'url': data.get('url', ''),
            'tags': data.get('tags', []),
            'urgency': str(data.get('urgency', '2')),  # Store as string, default to '2' (medium)
            'importance': str(data.get('importance', '2')),  # Store as string, default to '2' (medium)
            'reminder': data.get('reminder', ''),  # Store as string
            'category': data.get('comment', ''),  # Use category instead of comment
            'timestamp': datetime.utcnow()  # Use timestamp instead of created_at/updated_at
        }
        
        # 验证数据
        if not bookmark['title'] or not bookmark['url']:
            raise ValueError("标题和URL不能为空")

        # Validate urgency (1=high, 2=medium, 3=low)
        if bookmark['urgency'] not in ['1', '2', '3']:
            bookmark['urgency'] = '2'  # Default to medium

        # Validate importance (1-5 as strings)
        if bookmark['importance'] not in ['1', '2', '3', '4', '5']:
            bookmark['importance'] = '2'  # Default to medium
        
        # 插入书签
        result = self.collection.insert_one(bookmark)
        
        # 更新标签统计
        self._update_tag_stats(bookmark['tags'], increment=True)
        
        # 返回创建的书签
        bookmark['_id'] = result.inserted_id
        return self._format_bookmark(bookmark)
    
    def get_bookmarks(self, filters=None, page=1, limit=20, sort_by='timestamp', sort_order=-1):
        """获取书签列表"""
        self._ensure_collections()
        
        query = {}
        
        if filters:
            # 标签筛选
            if filters.get('tags'):
                tags = filters['tags'].split(',') if isinstance(filters['tags'], str) else filters['tags']
                query['tags'] = {'$in': tags}
            
            # 紧迫度筛选
            if filters.get('urgency'):
                query['urgency'] = filters['urgency']
            
            # 重要度筛选
            if filters.get('importance'):
                query['importance'] = str(filters['importance'])  # Compare as string
            
            # 搜索关键词
            if filters.get('search'):
                search_regex = re.compile(filters['search'], re.IGNORECASE)
                query['$or'] = [
                    {'title': search_regex},
                    {'category': search_regex},  # Use category instead of comment
                    {'url': search_regex}
                ]
            
            # 提醒日期筛选
            if filters.get('reminder_start') or filters.get('reminder_end'):
                reminder_query = {}
                if filters.get('reminder_start'):
                    reminder_query['$gte'] = datetime.fromisoformat(filters['reminder_start'])
                if filters.get('reminder_end'):
                    reminder_query['$lte'] = datetime.fromisoformat(filters['reminder_end'])
                if reminder_query:
                    query['reminder'] = reminder_query
        
        # 计算跳过的文档数
        skip = (page - 1) * limit
        
        # 排序字段映射
        sort_field = sort_by
        if sort_by not in ['timestamp', 'title', 'importance', 'urgency']:
            sort_field = 'timestamp'  # Use timestamp instead of created_at
        
        # 查询书签
        cursor = self.collection.find(query).sort(sort_field, sort_order).skip(skip).limit(limit)
        bookmarks = [self._format_bookmark(bookmark) for bookmark in cursor]
        
        # 获取总数
        total = self.collection.count_documents(query)
        
        return {
            'bookmarks': bookmarks,
            'total': total,
            'page': page,
            'limit': limit,
            'pages': (total + limit - 1) // limit
        }
    
    def get_bookmark_by_id(self, bookmark_id):
        """根据ID获取书签"""
        self._ensure_collections()
        
        try:
            bookmark = self.collection.find_one({'_id': ObjectId(bookmark_id)})
            return self._format_bookmark(bookmark) if bookmark else None
        except Exception:
            return None
    
    def update_bookmark(self, bookmark_id, data):
        """更新书签"""
        self._ensure_collections()
        
        try:
            # 获取原书签以更新标签统计
            old_bookmark = self.collection.find_one({'_id': ObjectId(bookmark_id)})
            if not old_bookmark:
                return None
            
            update_data = {
                'timestamp': datetime.utcnow()  # Update timestamp instead of updated_at
            }

            # 更新允许的字段
            allowed_fields = ['title', 'url', 'tags', 'urgency', 'importance', 'reminder', 'category']
            for field in allowed_fields:
                if field in data:
                    if field == 'comment':  # Map comment to category
                        update_data['category'] = data[field]
                    else:
                        update_data[field] = data[field]
            
            # 验证数据
            if 'urgency' in update_data and update_data['urgency'] not in ['1', '2', '3']:
                update_data['urgency'] = '2'  # Default to medium

            if 'importance' in update_data:
                if str(update_data['importance']) not in ['1', '2', '3', '4', '5']:
                    update_data['importance'] = '2'  # Default to medium
                else:
                    update_data['importance'] = str(update_data['importance'])  # Ensure string
            
            # 更新书签
            result = self.collection.update_one(
                {'_id': ObjectId(bookmark_id)},
                {'$set': update_data}
            )
            
            if result.modified_count > 0:
                # 更新标签统计
                old_tags = old_bookmark.get('tags', [])
                new_tags = update_data.get('tags', old_tags)
                if old_tags != new_tags:
                    self._update_tag_stats(old_tags, increment=False)
                    self._update_tag_stats(new_tags, increment=True)
                
                # 返回更新后的书签
                updated_bookmark = self.collection.find_one({'_id': ObjectId(bookmark_id)})
                return self._format_bookmark(updated_bookmark)
            
            return None
        except Exception:
            return None
    
    def delete_bookmark(self, bookmark_id):
        """删除书签"""
        self._ensure_collections()
        
        try:
            # 获取书签以更新标签统计
            bookmark = self.collection.find_one({'_id': ObjectId(bookmark_id)})
            if not bookmark:
                return False
            
            # 删除书签
            result = self.collection.delete_one({'_id': ObjectId(bookmark_id)})
            
            if result.deleted_count > 0:
                # 更新标签统计
                self._update_tag_stats(bookmark.get('tags', []), increment=False)
                return True
            
            return False
        except Exception:
            return False
    
    def delete_bookmarks_batch(self, bookmark_ids):
        """批量删除书签"""
        self._ensure_collections()
        
        try:
            object_ids = [ObjectId(bid) for bid in bookmark_ids]
            
            # 获取要删除的书签以更新标签统计
            bookmarks = list(self.collection.find({'_id': {'$in': object_ids}}))
            
            # 删除书签
            result = self.collection.delete_many({'_id': {'$in': object_ids}})
            
            if result.deleted_count > 0:
                # 更新标签统计
                for bookmark in bookmarks:
                    self._update_tag_stats(bookmark.get('tags', []), increment=False)
                
                return result.deleted_count
            
            return 0
        except Exception:
            return 0
    
    def get_tag_stats(self):
        """获取标签统计"""
        self._ensure_collections()
        
        pipeline = [
            {'$unwind': '$tags'},
            {'$group': {'_id': '$tags', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}},
            {'$project': {'name': '$_id', 'count': 1, '_id': 0}}
        ]
        
        result = list(self.collection.aggregate(pipeline))
        return result
    
    def get_stats(self):
        """获取统计信息"""
        self._ensure_collections()
        
        total_bookmarks = self.collection.count_documents({})
        
        # 紧迫度统计 (1=high, 2=medium, 3=low)
        urgency_stats = {}
        urgency_map = {'1': 'high', '2': 'medium', '3': 'low'}
        for urgency_code, urgency_name in urgency_map.items():
            urgency_stats[urgency_name] = self.collection.count_documents({'urgency': urgency_code})

        # 重要度统计 (stored as strings)
        importance_stats = {}
        for importance in range(1, 6):
            importance_stats[str(importance)] = self.collection.count_documents({'importance': str(importance)})
        
        # 标签数量
        tags_count = len(self.get_tag_stats())
        
        return {
            'total_bookmarks': total_bookmarks,
            'tags_count': tags_count,
            'urgency_stats': urgency_stats,
            'importance_stats': importance_stats
        }
    
    def export_all_data(self):
        """导出所有数据"""
        self._ensure_collections()
        
        bookmarks = list(self.collection.find({}))
        return [self._format_bookmark(bookmark) for bookmark in bookmarks]
    
    def import_data(self, bookmarks_data):
        """导入数据"""
        self._ensure_collections()
        
        try:
            # 清空现有数据
            self.collection.delete_many({})
            self.tags_collection.delete_many({})
            
            # 导入新数据
            imported_count = 0
            for bookmark_data in bookmarks_data:
                try:
                    # 移除_id字段，让MongoDB自动生成
                    if '_id' in bookmark_data:
                        del bookmark_data['_id']
                    
                    # 确保必要字段存在
                    bookmark_data.setdefault('timestamp', datetime.utcnow())
                    # Convert old format fields if they exist
                    if 'created_at' in bookmark_data and 'timestamp' not in bookmark_data:
                        bookmark_data['timestamp'] = bookmark_data['created_at']
                    if 'comment' in bookmark_data and 'category' not in bookmark_data:
                        bookmark_data['category'] = bookmark_data['comment']
                    
                    self.collection.insert_one(bookmark_data)
                    imported_count += 1
                except Exception as e:
                    print(f"导入书签失败: {e}")
                    continue
            
            # 重新计算标签统计
            self._rebuild_tag_stats()
            
            return imported_count
        except Exception as e:
            print(f"数据导入失败: {e}")
            return 0
    
    def _format_bookmark(self, bookmark):
        """格式化书签数据"""
        if not bookmark:
            return None

        # Convert urgency and importance to frontend format
        urgency_map = {'1': 'high', '2': 'medium', '3': 'low'}
        importance_value = bookmark.get('importance', '2')

        formatted = {
            'id': str(bookmark['_id']),
            'title': bookmark.get('title', ''),
            'url': bookmark.get('url', ''),
            'tags': bookmark.get('tags', []),
            'urgency': urgency_map.get(bookmark.get('urgency', '2'), 'medium'),
            'importance': int(importance_value) if importance_value.isdigit() else 2,
            'reminder': bookmark.get('reminder', ''),
            'comment': bookmark.get('category', ''),  # Map category to comment for frontend
            'created_at': bookmark.get('timestamp'),  # Map timestamp to created_at
            'updated_at': bookmark.get('timestamp')   # Use same timestamp for updated_at
        }

        # 格式化日期
        if formatted['reminder']:
            if isinstance(formatted['reminder'], str):
                formatted['reminder'] = formatted['reminder']
            elif hasattr(formatted['reminder'], 'isoformat'):
                formatted['reminder'] = formatted['reminder'].isoformat()
            else:
                formatted['reminder'] = str(formatted['reminder']) if formatted['reminder'] else None

        # Format timestamps - handle datetime objects properly
        if formatted['created_at']:
            if isinstance(formatted['created_at'], str):
                # Already a string, keep as is
                pass
            elif hasattr(formatted['created_at'], 'isoformat'):
                # It's a datetime object
                formatted['created_at'] = formatted['created_at'].isoformat()
            else:
                # Convert to string as fallback
                formatted['created_at'] = str(formatted['created_at'])

        if formatted['updated_at']:
            if isinstance(formatted['updated_at'], str):
                # Already a string, keep as is
                pass
            elif hasattr(formatted['updated_at'], 'isoformat'):
                # It's a datetime object
                formatted['updated_at'] = formatted['updated_at'].isoformat()
            else:
                # Convert to string as fallback
                formatted['updated_at'] = str(formatted['updated_at'])

        return formatted
    
    def _update_tag_stats(self, tags, increment=True):
        """更新标签统计"""
        self._ensure_collections()
        
        for tag in tags:
            if increment:
                self.tags_collection.update_one(
                    {'name': tag},
                    {'$inc': {'count': 1}, '$setOnInsert': {'created_at': datetime.utcnow()}},
                    upsert=True
                )
            else:
                result = self.tags_collection.update_one(
                    {'name': tag},
                    {'$inc': {'count': -1}}
                )
                # 如果计数为0或负数，删除标签
                tag_doc = self.tags_collection.find_one({'name': tag})
                if tag_doc and tag_doc.get('count', 0) <= 0:
                    self.tags_collection.delete_one({'name': tag})
    
    def _rebuild_tag_stats(self):
        """重建标签统计"""
        self._ensure_collections()
        
        # 清空标签统计
        self.tags_collection.delete_many({})
        
        # 重新计算
        pipeline = [
            {'$unwind': '$tags'},
            {'$group': {'_id': '$tags', 'count': {'$sum': 1}}}
        ]
        
        tag_stats = list(self.collection.aggregate(pipeline))
        
        for stat in tag_stats:
            self.tags_collection.insert_one({
                'name': stat['_id'],
                'count': stat['count'],
                'created_at': datetime.utcnow()
            })

# 全局书签模型实例
bookmark_model = BookmarkModel()

